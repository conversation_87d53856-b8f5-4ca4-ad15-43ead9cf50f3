import json
import re

def clean_unicode_characters(text):
    # Remove characters that are not printable or valid unicode
    return ''.join(ch for ch in text if ch.isprintable())

def clean_json_file(input_path, output_path):
    with open(input_path, 'r', encoding='utf-8', errors='replace') as f:
        raw_data = f.read()

    # Clean the entire text (useful if the whole file is dirty)
    cleaned_text = clean_unicode_characters(raw_data)

    # Try parsing as JSON to make sure it’s valid
    try:
        data = json.loads(cleaned_text)
    except json.JSONDecodeError as e:
        print("❌ JSON is still malformed after cleanup:", e)
        return

    # Save cleaned JSON back
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(data, f, indent=2, ensure_ascii=False)

    print("✅ Cleaned file saved to:", output_path)

# Change these file names if needed
clean_json_file('data.json', 'cleaned_data.json')
